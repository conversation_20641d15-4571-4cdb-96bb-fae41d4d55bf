Gevent is written and maintained by

  <PERSON>

and the contributors (ordered by the date of first contribution):

  <PERSON>
  Ludvig <PERSON>arrazzo
  Nicholas <PERSON>
  Dmitry Chechik
  <PERSON>
  Tommie G<PERSON>rt
  <PERSON>
  G<PERSON>ra Corretgé
  Oliver <PERSON>
  Jan-<PERSON>
  陈小玉
  <PERSON>

  See https://github.com/gevent/gevent/graphs/contributors for more info.

Gevent is inspired by and uses some code from eventlet which was written by

  <PERSON>

The win32util module is taken from Twisted. The tblib module is taken from python-tblib by <PERSON><PERSON>.

Some modules (local, ssl) contain code from the Python standard library.

If your code is used in gevent and you are not mentioned above, please contact the maintainer.
