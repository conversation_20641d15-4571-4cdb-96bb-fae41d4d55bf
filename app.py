from flask import Flask, render_template, request, jsonify, session
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import os
import time
from threading import Thread

from ai_agents.chaos_ai import ChaosAI
from ai_agents.red_ai import RedAI
from ai_agents.blue_ai import <PERSON>A<PERSON>
from ai_agents.vertex_client import VertexClient
from game.state_manager import GameStateManager
from game.infrastructure import Infrastructure
from game.scoring import Scoring
from game.commander_game import PlayerVs<PERSON>IGame
from game.ai_vs_ai_edu import AIEduGame

app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
socketio = SocketIO(app, async_mode='gevent')

import os

# Initialize Vertex AI Client
PROJECT_ID = "searce-playground-v2"
LOCATION = "global"

vertex_client = VertexClient(PROJECT_ID, LOCATION)

game_state_manager = GameStateManager()
chaos_ai = ChaosAI(vertex_client)
red_ai = RedAI(vertex_client)
blue_ai = BlueAI(vertex_client)
game_thread = None

# Global game instance for progressive loading
edu_game_instance = None
commander_game_instance = None
player_vs_ai_game_instance = None

def game_loop():
    """The main game loop."""
    while game_state_manager.get_state()['round_timer'] > 0:
        mode = game_state_manager.get_state()['mode']

        if mode == 'auto' or mode == 'pve':
            terraform_code = game_state_manager.get_state()['infrastructure']
            game_state_manager.game_state['vulnerable_code'] = terraform_code

            # Red AI attacks
            attack_result = red_ai.perform_attack(terraform_code)
            attack_success = attack_result.get('success', False)

            # Record attack statistics
            game_state_manager.record_attack(attack_success)

            # Enhanced attack messaging
            attack_type = attack_result.get('attack_type', 'Unknown')
            attack_description = Scoring.get_attack_description(attack_type, attack_success)
            game_state_manager.add_commentary("Red Team", attack_description)

            # Blue AI defends
            defense_result = blue_ai.defend(terraform_code, attack_result)
            defense_action = defense_result.get('defense_action', 'Monitoring')

            # Record defense statistics
            defense_success = defense_action != 'Monitoring'
            game_state_manager.record_defense(defense_success)

            # Enhanced defense messaging
            defense_description = Scoring.get_defense_description(defense_action)
            game_state_manager.add_commentary("Blue Team", defense_description)

            # Update Terraform code if defense was successful
            if defense_result and defense_result.get('terraform_code'):
                game_state_manager.game_state['infrastructure'] = defense_result.get('terraform_code', terraform_code)
                game_state_manager.game_state['secured_code'] = defense_result.get('terraform_code', terraform_code)

            # Calculate health impact
            health_impact = Scoring.calculate_health_impact(attack_result)
            if defense_success:
                defense_bonus = Scoring.calculate_defense_bonus(defense_result)
                health_impact += defense_bonus

            game_state_manager.update_health(health_impact)

            # Emit enhanced game state with statistics
            game_state = game_state_manager.get_state()
            game_state['stats'] = game_state_manager.get_game_stats()

            # Emit attack animation trigger for successful attacks
            if attack_success:
                socketio.emit('attack_animation', {
                    'type': attack_type,
                    'severity': abs(health_impact)
                })

            socketio.emit('game_update', game_state)

        time.sleep(15) # Attack interval
        game_state_manager.get_state()['round_timer'] -= 15

@socketio.on('start_game')
def start_game(data):
    global game_thread
    mode = data.get('mode', 'auto')
    game_state_manager.start_game(mode)
    
    # Generate initial infrastructure using Chaos AI
    terraform_code = chaos_ai.generate_infrastructure()
    infra = Infrastructure()
    infra.set_terraform_code(terraform_code)
    game_state_manager.game_state['infrastructure'] = terraform_code
    
    game_state_manager.add_commentary("Chaos AI", "Initial infrastructure created with Terraform code.")
    
    if game_thread is None or not game_thread.is_alive():
        game_thread = Thread(target=game_loop)
        game_thread.start()
        
    socketio.emit('game_update', game_state_manager.get_state())

@socketio.on('coach_blue')
def coach_blue(data):
    command = data.get('command')
    if command and game_state_manager.get_state()['mode'] == 'pve':
        # Map command to defense action
        command_mapping = {
            'patch_critical': 'Patch Critical',
            'monitor_traffic': 'Monitor Traffic',
            'firewall_lockdown': 'Firewall Lockdown'
        }

        defense_action = command_mapping.get(command, command)
        game_state_manager.add_commentary("Commander", f"Ordering Blue Team: {defense_action}")

        # Execute defense
        defense_result = blue_ai.defend(game_state_manager.get_state()['infrastructure'], command=command)

        # Record defense and calculate bonus
        defense_success = defense_action != 'Monitoring'
        game_state_manager.record_defense(defense_success)

        if defense_success:
            defense_bonus = Scoring.calculate_defense_bonus({'defense_action': defense_action})
            game_state_manager.update_health(defense_bonus)

        defense_description = Scoring.get_defense_description(defense_action)
        game_state_manager.add_commentary("Blue Team", defense_description)

        # Emit updated game state
        game_state = game_state_manager.get_state()
        game_state['stats'] = game_state_manager.get_game_stats()
        socketio.emit('game_update', game_state)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test_ai')
def test_ai():
    prompt = "What is the capital of France?"
    response = vertex_client.generate_text(prompt)
    return f"AI Response: {response}"

@app.route('/ai_vs_ai_edu')
def ai_vs_ai_edu():
    blue_ai = BlueAI(vertex_client)  # Use real Blue AI for defense
    red_ai = RedAI(vertex_client)    # Use real Red AI for attacks
    game = AIEduGame(blue_ai, red_ai, chaos_ai, max_rounds=8)
    while not game.is_over():
        game.play_round()
    summary = {
        'history': game.get_history(),
        'winner': game.get_winner()
    }
    return jsonify(summary)

@app.route('/commander_mode_view')
def commander_mode_view():
    return render_template('commander_mode.html')

@app.route('/commander_mode_reset')
def commander_mode_reset():
    global commander_game_instance
    commander_game_instance = None
    return jsonify({"status": "Commander game reset"})

@app.route('/commander/start_round', methods=['POST'])
def commander_start_round():
    global commander_game_instance
    if commander_game_instance is None:
        blue_ai = BlueAI(vertex_client)
        red_ai = RedAI(vertex_client)
        commander_game_instance = PlayerVsAIGame(blue_ai, red_ai, chaos_ai, max_rounds=10)
    if commander_game_instance.is_over():
        return jsonify({"error": "Game is over."}), 400
    round_start_data = commander_game_instance.start_mission_briefing()
    if round_start_data:
        return jsonify(round_start_data)
    else:
        return jsonify({"error": "Failed to start new round."}), 500

@app.route('/commander/run_command', methods=['POST'])
def commander_run_command():
    global commander_game_instance
    if commander_game_instance is None or commander_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400
    command = request.get_json().get('command', '')
    output_data = commander_game_instance.run_investigation_command(command)
    return jsonify(output_data)

@app.route('/commander/submit_hypothesis', methods=['POST'])
def commander_submit_hypothesis():
    global commander_game_instance
    if commander_game_instance is None or commander_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400
    data = request.get_json()
    if not data:
        return jsonify({"error": "No defense option provided."}), 400

    chosen_option_id = data.get('chosen_option_id', 0)
    response_time = data.get('response_time', 60.0)

    round_data = commander_game_instance.execute_player_decision(chosen_option_id, response_time)
    if round_data and not round_data.get('error'):
        return jsonify(round_data)
    else:
        return jsonify({"error": round_data.get('error', 'Failed to execute defense.')}), 500

@app.route('/commander_mode_winner')
def commander_mode_winner():
    global commander_game_instance
    if commander_game_instance and commander_game_instance.is_over():
        return jsonify({"winner": commander_game_instance.get_winner()})
    return jsonify({"winner": "Game in progress"})

# Player vs AI Mode Routes
@app.route('/player_vs_ai_mode')
def player_vs_ai_mode():
    return render_template('player_vs_ai_mode.html')

@app.route('/player_vs_ai/reset', methods=['POST'])
def player_vs_ai_reset():
    global player_vs_ai_game_instance
    player_vs_ai_game_instance = None
    return jsonify({"status": "Player vs AI game reset"})

@app.route('/player_vs_ai/start_briefing', methods=['POST'])
def player_vs_ai_start_briefing():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None:
        blue_ai = BlueAI(vertex_client)
        red_ai = RedAI(vertex_client)
        player_vs_ai_game_instance = PlayerVsAIGame(blue_ai, red_ai, chaos_ai, max_rounds=8)

    if player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game is over."}), 400

    briefing_data = player_vs_ai_game_instance.start_mission_briefing()
    if briefing_data:
        return jsonify(briefing_data)
    else:
        return jsonify({"error": "Failed to start mission briefing."}), 500

@app.route('/player_vs_ai/start_analysis', methods=['POST'])
def player_vs_ai_start_analysis():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None or player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400

    analysis_data = player_vs_ai_game_instance.start_analysis_phase()
    if analysis_data and not analysis_data.get('error'):
        return jsonify(analysis_data)
    else:
        return jsonify({"error": analysis_data.get('error', 'Failed to start analysis phase.')}), 500

@app.route('/player_vs_ai/run_command', methods=['POST'])
def player_vs_ai_run_command():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None or player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400

    command = request.get_json().get('command', '')
    if not command:
        return jsonify({"error": "No command provided."}), 400

    output_data = player_vs_ai_game_instance.run_investigation_command(command)
    if output_data and not output_data.get('error'):
        return jsonify(output_data)
    else:
        return jsonify({"error": output_data.get('error', 'Failed to execute command.')}), 500

@app.route('/player_vs_ai/get_defense_options', methods=['POST'])
def player_vs_ai_get_defense_options():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None or player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400

    defense_data = player_vs_ai_game_instance.get_defense_options()
    if defense_data and not defense_data.get('error'):
        return jsonify(defense_data)
    else:
        return jsonify({"error": defense_data.get('error', 'Failed to get defense options.')}), 500

@app.route('/player_vs_ai/execute_decision', methods=['POST'])
def player_vs_ai_execute_decision():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None or player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400

    data = request.get_json()
    if not data:
        return jsonify({"error": "No decision data provided."}), 400

    chosen_option_id = data.get('chosen_option_id')
    response_time = data.get('response_time', 60.0)

    if chosen_option_id is None:
        return jsonify({"error": "No defense option selected."}), 400

    result_data = player_vs_ai_game_instance.execute_player_decision(chosen_option_id, response_time)
    if result_data and not result_data.get('error'):
        return jsonify(result_data)
    else:
        return jsonify({"error": result_data.get('error', 'Failed to execute decision.')}), 500

@app.route('/player_vs_ai/get_hint', methods=['POST'])
def player_vs_ai_get_hint():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None or player_vs_ai_game_instance.is_over():
        return jsonify({"error": "Game not started or is over."}), 400

    data = request.get_json()
    hint_type = data.get('hint_type', 'general') if data else 'general'

    hint = player_vs_ai_game_instance.get_hint(hint_type)
    return jsonify(hint)

@app.route('/player_vs_ai/get_statistics', methods=['GET'])
def player_vs_ai_get_statistics():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance is None:
        return jsonify({"error": "Game not started."}), 400

    stats = player_vs_ai_game_instance.get_game_statistics()
    return jsonify(stats)

@app.route('/player_vs_ai/get_winner')
def player_vs_ai_get_winner():
    global player_vs_ai_game_instance
    if player_vs_ai_game_instance and player_vs_ai_game_instance.is_over():
        return jsonify({"winner": player_vs_ai_game_instance.get_winner()})
    return jsonify({"winner": "Game in progress"})

@app.route('/ai_vs_ai_edu_view')
def ai_vs_ai_edu_view():
    return render_template('ai_vs_ai_edu.html')

@app.route('/ai_vs_ai_edu_round/<int:round_num>')
def ai_vs_ai_edu_round(round_num):
    global edu_game_instance
    
    # Initialize game if not exists
    if edu_game_instance is None:
        blue_ai = BlueAI(vertex_client)
        red_ai = RedAI(vertex_client)
        edu_game_instance = AIEduGame(blue_ai, red_ai, chaos_ai, max_rounds=8)
    
    # Play rounds until we reach the requested round
    while edu_game_instance.current_round < round_num and not edu_game_instance.is_over():
        edu_game_instance.play_round()
    
    # Return the specific round data
    if round_num <= len(edu_game_instance.history):
        return jsonify(edu_game_instance.history[round_num - 1])
    else:
        return jsonify({"error": "Round not found"}), 404

@app.route('/ai_vs_ai_edu_winner')
def ai_vs_ai_edu_winner():
    global edu_game_instance
    if edu_game_instance:
        return jsonify({"winner": edu_game_instance.get_winner()})
    return jsonify({"winner": "Unknown"})

@app.route('/ai_vs_ai_edu_reset')
def ai_vs_ai_edu_reset():
    global edu_game_instance
    edu_game_instance = None
    return jsonify({"status": "Game reset"})

if __name__ == '__main__':
    socketio.run(app, debug=True)
