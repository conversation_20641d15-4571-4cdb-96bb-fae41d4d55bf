import random
import time
from typing import List, Dict, Any

class PlayerVsAIGame:
    def __init__(self, blue_ai, red_ai, chaos_ai, max_rounds=8, difficulty="normal"):
        self.blue_ai = blue_ai
        self.red_ai = red_ai
        self.chaos_ai = chaos_ai
        self.max_rounds = max_rounds
        self.current_round = 0
        self.difficulty = difficulty
        self.state = self._init_state()
        self.history = []
        self.player_stats = {
            'correct_predictions': 0,
            'total_predictions': 0,
            'defense_effectiveness': [],
            'response_times': [],
            'learning_progress': 0
        }

        # Mission-based scenarios with educational context
        self.missions = [
            {
                "name": "Operation: Database Breach",
                "theme": "Exposed Database on the Internet",
                "difficulty": 1,
                "learning_objective": "Understanding database security and access controls",
                "background": "Intelligence reports suggest attackers are targeting exposed databases. Your mission is to identify and secure vulnerable database instances."
            },
            {
                "name": "Operation: Cloud Storage",
                "theme": "Public Storage Bucket with Sensitive Data",
                "difficulty": 2,
                "learning_objective": "Cloud storage security and data classification",
                "background": "Sensitive corporate data may be exposed in misconfigured cloud storage. Investigate and secure all storage resources."
            },
            {
                "name": "Operation: Container Security",
                "theme": "Vulnerable Container Registry with Public Images",
                "difficulty": 2,
                "learning_objective": "Container security and image vulnerability management",
                "background": "Malicious actors are exploiting vulnerable container images. Secure the container infrastructure and implement proper scanning."
            },
            {
                "name": "Operation: API Gateway",
                "theme": "Unauthenticated API Gateway",
                "difficulty": 3,
                "learning_objective": "API security and authentication mechanisms",
                "background": "Critical APIs are exposed without proper authentication. Implement security controls before attackers exploit these endpoints."
            },
            {
                "name": "Operation: Load Balancer",
                "theme": "Misconfigured Load Balancer allowing Backend Exposure",
                "difficulty": 3,
                "learning_objective": "Network security and load balancer configuration",
                "background": "Backend services are directly accessible due to load balancer misconfigurations. Secure the network architecture."
            },
            {
                "name": "Operation: Privilege Escalation",
                "theme": "Overly Permissive IAM Roles for Public Services",
                "difficulty": 4,
                "learning_objective": "Identity and access management principles",
                "background": "Service accounts have excessive privileges that could lead to privilege escalation attacks. Apply principle of least privilege."
            },
            {
                "name": "Operation: Firewall Breach",
                "theme": "Insecure Network Firewall with 'allow all' rules",
                "difficulty": 4,
                "learning_objective": "Network security and firewall rule management",
                "background": "Network firewalls are configured with overly permissive rules. Implement proper network segmentation and access controls."
            },
            {
                "name": "Operation: Code Injection",
                "theme": "Web Application with a known RCE vulnerability",
                "difficulty": 5,
                "learning_objective": "Application security and vulnerability management",
                "background": "A critical remote code execution vulnerability has been discovered. Patch the application and implement additional security measures."
            }
        ]

    def _init_state(self):
        return {
            'blue_score': 0,
            'red_score': 0,
            'infrastructure': "# Awaiting mission start.",
            'system_health': 100,
            'vulnerabilities_found': 0,
            'defenses_deployed': 0,
            'round': 0,
            'player_confidence': 100,
            'ai_difficulty_modifier': 1.0,
            'streak_bonus': 0,
            'current_mission': None,
            'current_attack': None,
            'current_alert': None,
            'phase': 'briefing'  # briefing, analysis, decision, resolution
        }

    def start_mission_briefing(self):
        """Starts a new mission with briefing phase."""
        if self.is_over():
            return None

        mission = self.missions[self.current_round]
        self.state['current_mission'] = mission
        self.state['phase'] = 'briefing'

        # Generate infrastructure for this mission
        infra_code = self.chaos_ai.generate_infrastructure(mission['theme'])
        self.state['infrastructure'] = infra_code

        # Adjust AI difficulty based on player performance
        self._adjust_ai_difficulty()

        return {
            'mission': mission,
            'round': self.current_round + 1,
            'system_health': self.state['system_health'],
            'blue_score': self.state['blue_score'],
            'red_score': self.state['red_score'],
            'player_confidence': self.state['player_confidence'],
            'infrastructure': infra_code,
            'phase': 'briefing'
        }

    def start_analysis_phase(self):
        """Begins the analysis phase where AI attacks and player investigates."""
        if self.state['phase'] != 'briefing':
            return {"error": "Invalid phase transition"}

        self.state['phase'] = 'analysis'

        # AI performs attack
        attack_result = self.red_ai.perform_attack(self.state['infrastructure'])
        self.state['current_attack'] = attack_result

        # Generate realistic security alert
        alert = self._generate_enhanced_alert(attack_result)
        self.state['current_alert'] = alert

        # Provide investigation tools and hints
        investigation_tools = self._generate_investigation_tools()

        return {
            'alert': alert,
            'investigation_tools': investigation_tools,
            'phase': 'analysis',
            'time_limit': 120,  # 2 minutes for analysis
            'hints_available': 3
        }

    def _adjust_ai_difficulty(self):
        """Dynamically adjust AI difficulty based on player performance."""
        if len(self.player_stats['defense_effectiveness']) > 0:
            avg_effectiveness = sum(self.player_stats['defense_effectiveness']) / len(self.player_stats['defense_effectiveness'])

            if avg_effectiveness > 8:  # Player doing very well
                self.state['ai_difficulty_modifier'] = min(1.5, self.state['ai_difficulty_modifier'] + 0.1)
            elif avg_effectiveness < 5:  # Player struggling
                self.state['ai_difficulty_modifier'] = max(0.7, self.state['ai_difficulty_modifier'] - 0.1)

    def _generate_enhanced_alert(self, attack_result):
        """Generate a realistic security alert with multiple clues."""
        attack_type = attack_result.get('attack_type', 'Unknown Attack')
        severity = attack_result.get('severity', 5)

        # Base alert information
        alert = {
            'timestamp': time.time(),
            'severity': severity,
            'title': f"Security Incident: {attack_type}",
            'description': attack_result.get('message', 'Suspicious activity detected'),
            'affected_systems': self._get_affected_systems(attack_result),
            'indicators': self._generate_attack_indicators(attack_result),
            'recommended_actions': self._get_recommended_actions(attack_type)
        }

        return alert

    def _generate_investigation_tools(self):
        """Provide investigation tools and commands for the player."""
        return {
            'available_commands': [
                {'name': 'netstat', 'description': 'Show network connections', 'cost': 5},
                {'name': 'ps aux', 'description': 'List running processes', 'cost': 3},
                {'name': 'tail /var/log/auth.log', 'description': 'Check authentication logs', 'cost': 4},
                {'name': 'iptables -L', 'description': 'Show firewall rules', 'cost': 6},
                {'name': 'ss -tulpn', 'description': 'Show listening ports', 'cost': 4},
                {'name': 'find / -name "*.php" -mtime -1', 'description': 'Find recently modified files', 'cost': 8}
            ],
            'analysis_budget': 50,
            'forensic_tools': [
                'Network Traffic Analyzer',
                'Log Correlation Engine',
                'Vulnerability Scanner',
                'Threat Intelligence Feed'
            ]
        }

    def _get_affected_systems(self, attack_result):
        """Generate list of potentially affected systems."""
        systems = ['web-server-01', 'database-primary', 'api-gateway']
        attack_type = attack_result.get('attack_type', '').lower()

        if 'database' in attack_type:
            return ['database-primary', 'database-replica']
        elif 'web' in attack_type or 'http' in attack_type:
            return ['web-server-01', 'web-server-02', 'load-balancer']
        elif 'api' in attack_type:
            return ['api-gateway', 'auth-service']
        else:
            return systems[:2]  # Default to first two systems

    def _generate_attack_indicators(self, attack_result):
        """Generate technical indicators for the attack."""
        indicators = []
        attack_type = attack_result.get('attack_type', '').lower()

        if 'sql' in attack_type:
            indicators = [
                'Unusual database query patterns detected',
                'Multiple failed authentication attempts',
                'Suspicious SQL syntax in web logs'
            ]
        elif 'ddos' in attack_type:
            indicators = [
                'Abnormal traffic volume from multiple IPs',
                'High CPU usage on load balancers',
                'Connection timeout errors increasing'
            ]
        elif 'privilege' in attack_type:
            indicators = [
                'Unusual sudo command usage',
                'Service account accessing sensitive files',
                'Elevation of privileges detected'
            ]
        else:
            indicators = [
                'Anomalous network traffic patterns',
                'Unexpected system resource usage',
                'Security policy violations detected'
            ]

        return indicators

    def _get_recommended_actions(self, attack_type):
        """Get recommended defensive actions based on attack type."""
        recommendations = {
            'SQL Injection': [
                'Implement input validation',
                'Use parameterized queries',
                'Apply principle of least privilege to database accounts'
            ],
            'DDoS Attack': [
                'Enable rate limiting',
                'Configure auto-scaling',
                'Implement traffic filtering'
            ],
            'Privilege Escalation': [
                'Review and restrict sudo permissions',
                'Implement proper access controls',
                'Monitor privileged account usage'
            ]
        }

        return recommendations.get(attack_type, [
            'Investigate the incident thoroughly',
            'Implement additional monitoring',
            'Review security policies'
        ])

    def run_investigation_command(self, command_str: str):
        """Enhanced investigation system with realistic command outputs."""
        if self.state['phase'] != 'analysis':
            return {"error": "Investigation only available during analysis phase"}

        # Check if command is available
        available_commands = self._generate_investigation_tools()['available_commands']
        command_info = next((cmd for cmd in available_commands if cmd['name'] == command_str), None)

        if not command_info:
            return {
                "output": f"Command '{command_str}' not recognized. Available commands: {', '.join([cmd['name'] for cmd in available_commands])}",
                "success": False
            }

        attack = self.state.get('current_attack')
        if not attack:
            return {"output": "Error: No active incident to analyze.", "success": False}

        # Generate realistic command output based on the attack
        output = self._get_enhanced_command_output(command_str, attack, self.state['infrastructure'])

        # Track investigation progress
        self._update_investigation_progress(command_str, attack)

        return {
            "output": output,
            "success": True,
            "command": command_str,
            "relevance_score": self._calculate_command_relevance(command_str, attack)
        }

    def get_defense_options(self):
        """Generate multiple defense options for the player to choose from."""
        if self.state['phase'] != 'analysis':
            return {"error": "Defense options only available after analysis"}

        attack = self.state.get('current_attack')
        if not attack:
            return {"error": "No active attack to defend against"}

        # Generate 3-4 defense options with varying effectiveness
        options = self.blue_ai.propose_options(
            self.state['infrastructure'],
            attack,
            self._generate_intelligence_report()
        )

        # Add educational information to each option
        for i, option in enumerate(options):
            option['id'] = i
            option['learning_tip'] = self._get_learning_tip(option, attack)
            option['risk_assessment'] = self._assess_option_risk(option, attack)

        self.state['phase'] = 'decision'
        return {
            'options': options,
            'phase': 'decision',
            'time_limit': 90,  # 90 seconds to decide
            'attack_summary': self._generate_attack_summary(attack)
        }

    def execute_player_decision(self, chosen_option_id: int, response_time: float):
        """Execute the player's chosen defense and resolve the round."""
        if self.state['phase'] != 'decision':
            return {"error": "Invalid phase for decision execution"}

        attack = self.state.get('current_attack')
        if not attack:
            return {"error": "No active attack to defend against"}

        # Get defense options again to validate choice
        options = self.blue_ai.propose_options(
            self.state['infrastructure'],
            attack,
            self._generate_intelligence_report()
        )

        if chosen_option_id >= len(options):
            return {"error": "Invalid option selected"}

        chosen_option = options[chosen_option_id]

        # Execute the defense
        defense_result = self.blue_ai.implement_choice(
            self.state['infrastructure'],
            attack,
            chosen_option,
            self._generate_intelligence_report()
        )

        # Calculate round results with educational feedback
        round_result = self._resolve_enhanced_round(attack, defense_result, chosen_option, response_time)

        # Update player stats and game state
        self._update_player_stats(round_result, response_time)
        self._update_game_state(round_result)

        # Prepare round summary with learning elements
        round_summary = self._generate_round_summary(attack, defense_result, round_result, chosen_option)

        self.current_round += 1
        self.state['round'] = self.current_round
        self.state['phase'] = 'resolution'

        self.history.append(round_summary)

        return round_summary

    def _generate_intelligence_report(self):
        """Generate an intelligence report for the AI to use."""
        attack = self.state.get('current_attack', {})
        return f"Security incident detected: {attack.get('attack_type', 'Unknown')}. Investigating potential {attack.get('severity', 5)}/10 severity threat."

    def _get_learning_tip(self, option, attack):
        """Generate educational tips for each defense option."""
        tips = {
            'Firewall': "Firewalls control network traffic based on predetermined security rules. They're your first line of defense.",
            'Patch': "Regular patching closes known vulnerabilities. Always prioritize critical security patches.",
            'Monitor': "Continuous monitoring helps detect threats early. The sooner you detect, the faster you can respond.",
            'Isolate': "Network isolation contains threats and prevents lateral movement. Quarantine suspicious systems immediately."
        }

        option_name = option.get('name', '').split(':')[0]
        return tips.get(option_name, "Every security decision involves trade-offs between security, usability, and performance.")

    def _assess_option_risk(self, option, attack):
        """Assess the risk level of each defense option."""
        effectiveness = option.get('effectiveness', 5)

        if effectiveness >= 8:
            return {"level": "Low", "description": "High chance of successfully mitigating the threat"}
        elif effectiveness >= 6:
            return {"level": "Medium", "description": "Moderate chance of success, may need additional measures"}
        else:
            return {"level": "High", "description": "Lower effectiveness, consider combining with other defenses"}

    def _generate_attack_summary(self, attack):
        """Generate a concise attack summary for decision making."""
        return {
            'type': attack.get('attack_type', 'Unknown'),
            'severity': attack.get('severity', 5),
            'target': attack.get('target', 'System'),
            'method': attack.get('method', 'Various techniques'),
            'urgency': 'High' if attack.get('severity', 5) >= 7 else 'Medium'
        }

    def _resolve_enhanced_round(self, attack_result, defense_result, chosen_option, response_time):
        """Enhanced round resolution with educational feedback."""
        attack_effectiveness = attack_result.get('severity', 5) * self.state['ai_difficulty_modifier']
        defense_effectiveness = defense_result.get('effectiveness', 5)

        # Factor in response time (faster responses get bonus)
        time_bonus = max(0, (90 - response_time) / 90 * 2)  # Up to 2 point bonus
        defense_effectiveness += time_bonus

        # Calculate damage and success
        attack_success = attack_effectiveness > defense_effectiveness
        health_damage = max(0, int(attack_effectiveness - defense_effectiveness)) if attack_success else 0
        defense_bonus = max(0, int(defense_effectiveness - attack_effectiveness)) if not attack_success else 0

        # Apply streak bonuses
        if not attack_success:
            self.state['streak_bonus'] += 1
            defense_bonus += self.state['streak_bonus']
        else:
            self.state['streak_bonus'] = 0

        # Update scores and health
        red_points = health_damage + (5 if attack_success else 0)
        blue_points = defense_bonus + (10 if not attack_success else 0)

        self.state['system_health'] = max(0, self.state['system_health'] - health_damage + defense_bonus)
        self.state['system_health'] = min(100, self.state['system_health'])

        # Determine round winner
        round_winner = "Draw"
        if red_points > blue_points:
            round_winner = "AI Red Team"
            self.state['player_confidence'] = max(0, self.state['player_confidence'] - 10)
        elif blue_points > red_points:
            round_winner = "Player Blue Team"
            self.state['player_confidence'] = min(100, self.state['player_confidence'] + 5)

        return {
            'health_damage': health_damage,
            'defense_bonus': defense_bonus,
            'attack_success': attack_success,
            'defense_effectiveness': defense_effectiveness,
            'attack_effectiveness': attack_effectiveness,
            'time_bonus': time_bonus,
            'streak_bonus': self.state['streak_bonus'],
            'round_winner': round_winner,
            'red_points': red_points,
            'blue_points': blue_points
        }

    def _update_player_stats(self, round_result, response_time):
        """Update player performance statistics."""
        self.player_stats['total_predictions'] += 1
        self.player_stats['defense_effectiveness'].append(round_result['defense_effectiveness'])
        self.player_stats['response_times'].append(response_time)

        if round_result['round_winner'] == "Player Blue Team":
            self.player_stats['correct_predictions'] += 1

        # Calculate learning progress
        if len(self.player_stats['defense_effectiveness']) >= 3:
            recent_avg = sum(self.player_stats['defense_effectiveness'][-3:]) / 3
            self.player_stats['learning_progress'] = min(100, recent_avg * 10)

    def _update_game_state(self, round_result):
        """Update the overall game state."""
        self.state['red_score'] += round_result['red_points']
        self.state['blue_score'] += round_result['blue_points']

        if round_result['attack_success']:
            self.state['vulnerabilities_found'] += 1
        else:
            self.state['defenses_deployed'] += 1

    def _generate_round_summary(self, attack, defense, round_result, chosen_option):
        """Generate comprehensive round summary with educational content."""
        mission = self.state['current_mission']

        return {
            'round': self.current_round + 1,
            'mission': mission,
            'attack': attack,
            'defense': defense,
            'chosen_option': chosen_option,
            'result': round_result,
            'system_health': self.state['system_health'],
            'blue_score': self.state['blue_score'],
            'red_score': self.state['red_score'],
            'player_confidence': self.state['player_confidence'],
            'learning_insights': self._generate_learning_insights(attack, defense, round_result),
            'performance_feedback': self._generate_performance_feedback(round_result),
            'next_steps': self._generate_next_steps()
        }

    def _generate_learning_insights(self, attack, defense, round_result):
        """Generate educational insights based on the round."""
        insights = []

        if round_result['time_bonus'] > 1:
            insights.append("Excellent response time! Quick decision-making is crucial in cybersecurity incidents.")
        elif round_result['time_bonus'] < 0.5:
            insights.append("Consider practicing faster threat assessment. In real incidents, time is critical.")

        if round_result['defense_effectiveness'] > 8:
            insights.append("Outstanding defense choice! You correctly identified the most effective countermeasure.")
        elif round_result['defense_effectiveness'] < 5:
            insights.append("This defense was less effective. Consider the attack vector and choose targeted countermeasures.")

        if round_result['streak_bonus'] > 2:
            insights.append(f"Impressive streak! {round_result['streak_bonus']} consecutive successful defenses shows strong pattern recognition.")

        return insights

    def _generate_performance_feedback(self, round_result):
        """Generate performance feedback for the player."""
        accuracy = (self.player_stats['correct_predictions'] / max(1, self.player_stats['total_predictions'])) * 100

        feedback = {
            'accuracy': round(accuracy, 1),
            'avg_response_time': round(sum(self.player_stats['response_times']) / max(1, len(self.player_stats['response_times'])), 1),
            'learning_progress': self.player_stats['learning_progress'],
            'confidence_level': self.state['player_confidence']
        }

        if accuracy >= 80:
            feedback['grade'] = 'A'
            feedback['message'] = 'Exceptional cybersecurity skills!'
        elif accuracy >= 60:
            feedback['grade'] = 'B'
            feedback['message'] = 'Good security awareness, keep improving!'
        else:
            feedback['grade'] = 'C'
            feedback['message'] = 'Focus on understanding attack patterns and defense strategies.'

        return feedback

    def _generate_next_steps(self):
        """Generate recommendations for the next round."""
        if self.current_round + 1 >= self.max_rounds:
            return ["Prepare for final mission debrief", "Review overall performance", "Celebrate your achievements!"]

        next_mission = self.missions[self.current_round + 1] if self.current_round + 1 < len(self.missions) else None
        if next_mission:
            return [
                f"Prepare for {next_mission['name']}",
                f"Study: {next_mission['learning_objective']}",
                "Review previous round insights"
            ]

        return ["Continue defending against cyber threats", "Apply lessons learned", "Stay vigilant"]

    def _get_enhanced_command_output(self, command_str: str, attack: Dict[str, Any], infrastructure: str) -> str:
        """Generate realistic and educational command outputs."""
        command_parts = command_str.lower().split()
        cmd = command_parts[0] if command_parts else ""

        attack_type = attack.get('attack_type', '').lower()

        if cmd == 'netstat':
            if 'ddos' in attack_type:
                return """Active Internet connections (servers and established)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:80              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:443             0.0.0.0:*               LISTEN
tcp      512    512 ************:80         ************:54321      ESTABLISHED
tcp      512    512 ************:80         ************:54322      ESTABLISHED
tcp      512    512 ************:80         ************:54323      ESTABLISHED
[WARNING] Unusual number of connections from similar IP ranges detected"""
            else:
                return """Active Internet connections (servers and established)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:80              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:443             0.0.0.0:*               LISTEN
tcp        0      0 ************:22         ********:45678          ESTABLISHED"""

        elif cmd == 'ps' or 'ps aux' in command_str:
            if 'malware' in attack_type or 'backdoor' in attack_type:
                return """USER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root         1  0.0  0.1  19356  1544 ?        Ss   08:00   0:01 /sbin/init
www-data  1234  0.1  0.5  12345  6789 ?        S    08:30   0:02 /usr/bin/apache2
root      5678  2.5  1.2  45678  9876 ?        R    12:45   0:15 /tmp/.hidden/sys_update
[SUSPICIOUS] Process 'sys_update' running from /tmp with high CPU usage"""
            else:
                return """USER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root         1  0.0  0.1  19356  1544 ?        Ss   08:00   0:01 /sbin/init
www-data  1234  0.1  0.5  12345  6789 ?        S    08:30   0:02 /usr/bin/apache2
mysql     2345  0.3  2.1  67890 12345 ?        S    08:31   0:05 /usr/sbin/mysqld"""

        elif 'auth.log' in command_str or 'log' in cmd:
            if 'brute' in attack_type or 'password' in attack_type:
                return """Dec 15 12:45:01 server sshd[1234]: Failed password for admin from ************ port 22 ssh2
Dec 15 12:45:03 server sshd[1235]: Failed password for admin from ************ port 22 ssh2
Dec 15 12:45:05 server sshd[1236]: Failed password for admin from ************ port 22 ssh2
Dec 15 12:45:07 server sshd[1237]: Failed password for root from ************ port 22 ssh2
Dec 15 12:45:15 server sshd[1238]: Accepted password for admin from ************ port 22 ssh2
[ALERT] Multiple failed login attempts followed by successful authentication"""
            else:
                return """Dec 15 12:30:01 server sshd[1234]: Accepted publickey for user from ******** port 22 ssh2
Dec 15 12:35:22 server sudo: user : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Dec 15 12:40:15 server sshd[1235]: Connection closed by ******** port 22"""

        elif cmd == 'iptables':
            return """Chain INPUT (policy ACCEPT)
target     prot opt source               destination
ACCEPT     all  --  anywhere             anywhere             ctstate RELATED,ESTABLISHED
ACCEPT     icmp --  anywhere             anywhere
ACCEPT     all  --  anywhere             anywhere
ACCEPT     tcp  --  anywhere             anywhere             tcp dpt:ssh
ACCEPT     tcp  --  anywhere             anywhere             tcp dpt:http
ACCEPT     tcp  --  anywhere             anywhere             tcp dpt:https
[NOTE] Default policy is ACCEPT - consider changing to DROP for better security"""

        elif cmd == 'ss' or 'tulpn' in command_str:
            if 'backdoor' in attack_type:
                return """Netid  State      Recv-Q Send-Q Local Address:Port               Peer Address:Port
tcp    LISTEN     0      128          *:22                      *:*
tcp    LISTEN     0      128          *:80                      *:*
tcp    LISTEN     0      128          *:443                     *:*
tcp    LISTEN     0      50           *:31337                   *:*
[SUSPICIOUS] Unusual service listening on port 31337"""
            else:
                return """Netid  State      Recv-Q Send-Q Local Address:Port               Peer Address:Port
tcp    LISTEN     0      128          *:22                      *:*
tcp    LISTEN     0      128          *:80                      *:*
tcp    LISTEN     0      128          *:443                     *:*
tcp    LISTEN     0      50           *:3306                    *:*"""

        elif 'find' in cmd:
            if 'web' in attack_type or 'injection' in attack_type:
                return """/var/www/html/uploads/shell.php
/var/www/html/admin/config.php.bak
/tmp/webshell.php
[WARNING] Suspicious PHP files found in web directories"""
            else:
                return """/var/log/apache2/access.log
/var/log/apache2/error.log
/etc/apache2/apache2.conf"""

        else:
            return f"Command '{command_str}' executed. No immediate security concerns detected in output."

    def _update_investigation_progress(self, command: str, attack: Dict[str, Any]):
        """Track investigation progress and provide hints."""
        # This could be expanded to track which commands are most useful
        # for different types of attacks and provide learning feedback
        pass

    def _calculate_command_relevance(self, command: str, attack: Dict[str, Any]) -> int:
        """Calculate how relevant a command is to the current attack (1-10 scale)."""
        attack_type = attack.get('attack_type', '').lower()

        relevance_map = {
            'netstat': {'ddos': 9, 'network': 8, 'default': 6},
            'ps': {'malware': 9, 'backdoor': 8, 'privilege': 7, 'default': 5},
            'auth.log': {'brute': 10, 'password': 9, 'privilege': 8, 'default': 4},
            'iptables': {'firewall': 9, 'network': 8, 'ddos': 7, 'default': 5},
            'ss': {'backdoor': 9, 'network': 8, 'default': 6},
            'find': {'web': 8, 'injection': 9, 'malware': 7, 'default': 4}
        }

        cmd_relevance = relevance_map.get(command, {'default': 5})

        # Check for specific attack type matches
        for attack_keyword in attack_type.split():
            if attack_keyword in cmd_relevance:
                return cmd_relevance[attack_keyword]

        return cmd_relevance['default']

    def get_hint(self, hint_type: str):
        """Provide educational hints to help the player."""
        attack = self.state.get('current_attack', {})
        attack_type = attack.get('attack_type', '').lower()

        hints = {
            'investigation': {
                'sql': "Look for unusual database queries and check authentication logs for suspicious activity.",
                'ddos': "Monitor network connections and check for unusual traffic patterns from multiple sources.",
                'privilege': "Examine process lists and authentication logs for unauthorized privilege escalation.",
                'default': "Start with network analysis and system logs to identify the attack vector."
            },
            'defense': {
                'sql': "Consider input validation, parameterized queries, and database access restrictions.",
                'ddos': "Think about rate limiting, traffic filtering, and auto-scaling capabilities.",
                'privilege': "Focus on access controls, privilege restrictions, and monitoring.",
                'default': "Apply defense-in-depth principles with multiple security layers."
            },
            'learning': {
                'sql': "SQL injection exploits occur when user input is not properly validated before database queries.",
                'ddos': "DDoS attacks overwhelm systems with traffic. Mitigation requires traffic management and filtering.",
                'privilege': "Privilege escalation attacks exploit weak access controls to gain higher system privileges.",
                'default': "Cybersecurity requires understanding both attack methods and defensive strategies."
            }
        }

        hint_category = hints.get(hint_type, hints['learning'])

        # Find the most specific hint for the attack type
        for keyword in attack_type.split():
            if keyword in hint_category:
                return hint_category[keyword]

        return hint_category['default']

    def get_game_statistics(self):
        """Get comprehensive game statistics for analysis."""
        total_rounds = len(self.history)

        if total_rounds == 0:
            return {
                'rounds_played': 0,
                'accuracy': 0,
                'avg_response_time': 0,
                'learning_progress': 0,
                'difficulty_level': self.difficulty
            }

        return {
            'rounds_played': total_rounds,
            'accuracy': (self.player_stats['correct_predictions'] / max(1, self.player_stats['total_predictions'])) * 100,
            'avg_response_time': sum(self.player_stats['response_times']) / len(self.player_stats['response_times']),
            'avg_defense_effectiveness': sum(self.player_stats['defense_effectiveness']) / len(self.player_stats['defense_effectiveness']),
            'learning_progress': self.player_stats['learning_progress'],
            'player_confidence': self.state['player_confidence'],
            'streak_bonus': self.state['streak_bonus'],
            'difficulty_level': self.difficulty,
            'missions_completed': total_rounds,
            'total_missions': self.max_rounds
        }

    def reset_game(self):
        """Reset the game to initial state for a new session."""
        self.current_round = 0
        self.state = self._init_state()
        self.history = []
        self.player_stats = {
            'correct_predictions': 0,
            'total_predictions': 0,
            'defense_effectiveness': [],
            'response_times': [],
            'learning_progress': 0
        }

        return {
            'message': 'Game reset successfully',
            'ready_for_new_mission': True
        }

    def is_over(self):
        """Check if the game is over."""
        return self.current_round >= self.max_rounds or self.state['system_health'] <= 0

    def get_current_state(self):
        """Get the current game state for the frontend."""
        return {
            'round': self.current_round,
            'max_rounds': self.max_rounds,
            'phase': self.state.get('phase', 'briefing'),
            'system_health': self.state['system_health'],
            'blue_score': self.state['blue_score'],
            'red_score': self.state['red_score'],
            'player_confidence': self.state['player_confidence'],
            'current_mission': self.state.get('current_mission'),
            'is_over': self.is_over(),
            'player_stats': self.player_stats
        }

    def get_winner(self):
        if self.state['system_health'] <= 0:
            return 'Red AI (System Compromised)'
        elif self.state['blue_score'] > self.state['red_score']:
            return 'You & Blue AI (Defenders Win)'
        elif self.state['red_score'] > self.state['blue_score']:
            return 'Red AI (Attacker Wins)'
        else:
            return 'Draw'

    def get_history(self):
        return self.history