<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Player vs AI Mode - Cybersecurity Training</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .mission-briefing {
            background: linear-gradient(135deg, var(--dark-panel) 0%, rgba(26, 26, 26, 0.9) 100%);
            border: 2px solid var(--blue-primary);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 0 20px var(--blue-glow);
        }
        
        .mission-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 900;
            color: var(--blue-primary);
            text-shadow: 0 0 10px var(--blue-glow);
            margin-bottom: 1rem;
        }
        
        .phase-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }
        
        .phase-step {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
        }
        
        .phase-step.active {
            background: var(--blue-primary);
            color: var(--dark-bg);
            box-shadow: 0 0 15px var(--blue-glow);
        }
        
        .phase-step.completed {
            background: var(--green-success);
            color: var(--dark-bg);
        }
        
        .investigation-panel {
            background: linear-gradient(135deg, var(--dark-panel) 0%, rgba(26, 26, 26, 0.9) 100%);
            border: 2px solid var(--yellow-warning);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 0 15px rgba(255, 170, 0, 0.3);
        }
        
        .alert-panel {
            background: linear-gradient(135deg, rgba(255, 0, 64, 0.1) 0%, rgba(255, 0, 64, 0.05) 100%);
            border: 2px solid var(--red-primary);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 0 15px var(--red-glow);
            animation: alertPulse 2s infinite;
        }
        
        @keyframes alertPulse {
            0%, 100% { box-shadow: 0 0 15px var(--red-glow); }
            50% { box-shadow: 0 0 25px var(--red-glow); }
        }
        
        .command-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .command-btn {
            background: linear-gradient(135deg, var(--dark-panel) 0%, rgba(26, 26, 26, 0.9) 100%);
            border: 2px solid var(--blue-secondary);
            border-radius: 10px;
            padding: 1rem;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
            text-align: left;
        }
        
        .command-btn:hover {
            border-color: var(--blue-primary);
            box-shadow: 0 0 15px var(--blue-glow);
            transform: translateY(-2px);
        }
        
        .command-btn.used {
            opacity: 0.6;
            border-color: var(--text-muted);
        }
        
        .defense-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .defense-option {
            background: linear-gradient(135deg, var(--dark-panel) 0%, rgba(26, 26, 26, 0.9) 100%);
            border: 2px solid var(--blue-secondary);
            border-radius: 15px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .defense-option:hover {
            border-color: var(--blue-primary);
            box-shadow: 0 0 20px var(--blue-glow);
            transform: translateY(-3px);
        }
        
        .defense-option.selected {
            border-color: var(--green-success);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }
        
        .confidence-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-top: 1rem;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--red-primary) 0%, var(--yellow-warning) 50%, var(--green-success) 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .timer-display {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 900;
            color: var(--yellow-warning);
            text-align: center;
            margin: 1rem 0;
            text-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
        }
        
        .timer-display.urgent {
            color: var(--red-primary);
            animation: timerPulse 1s infinite;
        }
        
        @keyframes timerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .learning-tip {
            background: rgba(0, 212, 255, 0.1);
            border-left: 4px solid var(--blue-primary);
            padding: 1rem;
            margin-top: 1rem;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }
        
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .stat-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            border: 1px solid var(--dark-border);
        }
        
        .stat-value {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            font-weight: 900;
            color: var(--blue-primary);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
        }
        
        .progress-ring-circle {
            fill: none;
            stroke: var(--blue-primary);
            stroke-width: 4;
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .hint-panel {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--green-success);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }
        
        .hint-panel.show {
            display: block;
            animation: fadeInUp 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="header-bar">
            <a href="/" class="home-link" title="Back to Home"><i class="fas fa-home"></i></a>
            <span><i class="fas fa-graduation-cap"></i> Player vs AI Training</span>
        </div>
        
        <!-- Phase Indicator -->
        <div class="phase-indicator">
            <div class="phase-step" id="phase-briefing">
                <i class="fas fa-clipboard-list"></i><br>Briefing
            </div>
            <div class="phase-step" id="phase-analysis">
                <i class="fas fa-search"></i><br>Analysis
            </div>
            <div class="phase-step" id="phase-decision">
                <i class="fas fa-shield-alt"></i><br>Decision
            </div>
            <div class="phase-step" id="phase-resolution">
                <i class="fas fa-trophy"></i><br>Resolution
            </div>
        </div>
        
        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar" id="mission-progress-bar"></div>
            <div class="progress-text" id="mission-progress-text">Mission 0 / 8</div>
        </div>
        
        <!-- Main Content Area -->
        <div id="main-content" class="row">
            <!-- Left Panel: Game Content -->
            <div class="col-lg-8">
                <div id="game-panel" class="cyber-panel">
                    <div class="panel-header">
                        <i class="fas fa-gamepad"></i> Mission Control
                    </div>
                    <div class="panel-content" id="game-content">
                        <div class="text-center">
                            <button class="cyber-btn blue-btn" onclick="startFirstMission()">
                                <i class="fas fa-play"></i> Begin Training
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel: Stats and Info -->
            <div class="col-lg-4">
                <!-- Player Stats -->
                <div class="cyber-panel">
                    <div class="panel-header">
                        <i class="fas fa-chart-line"></i> Performance
                    </div>
                    <div class="panel-content">
                        <div class="performance-stats" id="performance-stats">
                            <div class="stat-card">
                                <div class="stat-value" id="accuracy-stat">0%</div>
                                <div class="stat-label">Accuracy</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="confidence-stat">100</div>
                                <div class="stat-label">Confidence</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="streak-stat">0</div>
                                <div class="stat-label">Streak</div>
                            </div>
                        </div>
                        
                        <!-- Learning Progress -->
                        <div class="text-center mt-3">
                            <svg class="progress-ring">
                                <circle class="progress-ring-circle" cx="30" cy="30" r="25" id="progress-circle"></circle>
                            </svg>
                            <div class="stat-label">Learning Progress</div>
                            <div class="stat-value" id="learning-progress">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- System Health -->
                <div class="cyber-panel">
                    <div class="panel-header">
                        <i class="fas fa-heartbeat"></i> System Status
                    </div>
                    <div class="panel-content">
                        <div class="health-display">
                            <div class="health-bar">
                                <div class="health-fill" id="health-fill" style="width: 100%"></div>
                            </div>
                            <div class="health-text" id="health-text">100%</div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between">
                                <span>Blue Score:</span>
                                <span class="text-blue" id="blue-score">0</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>AI Score:</span>
                                <span class="text-red" id="red-score">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Audio Elements -->
    <audio id="alert-sound" preload="auto">
        <source src="/static/audio/alert.mp3" type="audio/mpeg">
    </audio>
    <audio id="success-sound" preload="auto">
        <source src="/static/audio/success.mp3" type="audio/mpeg">
    </audio>
    <audio id="timer-sound" preload="auto">
        <source src="/static/audio/timer.mp3" type="audio/mpeg">
    </audio>

    <script>
        // Game state variables
        let currentMission = 0;
        let maxMissions = 8;
        let gamePhase = 'briefing';
        let investigationTimer = null;
        let decisionTimer = null;
        let usedCommands = new Set();
        let selectedDefenseOption = null;
        let missionStartTime = null;
        let hintsUsed = 0;

        // Initialize the game
        document.addEventListener('DOMContentLoaded', function() {
            updateProgressBar(0, maxMissions);
            updatePhaseIndicator('briefing');
        });

        // Start the first mission
        async function startFirstMission() {
            try {
                const response = await fetch('/player_vs_ai/start_briefing', { method: 'POST' });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                currentMission = data.round;
                renderMissionBriefing(data);
                updateProgressBar(currentMission, maxMissions);
                updatePhaseIndicator('briefing');

            } catch (error) {
                console.error('Error starting mission:', error);
                showError('Failed to start mission. Please try again.');
            }
        }

        // Render mission briefing phase
        function renderMissionBriefing(data) {
            const gameContent = document.getElementById('game-content');
            const mission = data.mission;

            gameContent.innerHTML = `
                <div class="mission-briefing">
                    <div class="mission-title">
                        <i class="fas fa-flag"></i> ${mission.name}
                    </div>
                    <div class="mb-3">
                        <strong>Difficulty:</strong>
                        <span class="difficulty-${mission.difficulty}">
                            ${'★'.repeat(mission.difficulty)}${'☆'.repeat(5-mission.difficulty)}
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>Learning Objective:</strong> ${mission.learning_objective}
                    </div>
                    <div class="mb-3">
                        <strong>Mission Background:</strong><br>
                        ${mission.background}
                    </div>
                    <div class="mb-3">
                        <strong>Infrastructure Overview:</strong>
                        <pre class="code-block mt-2">${data.infrastructure}</pre>
                    </div>
                    <div class="text-center mt-4">
                        <button class="cyber-btn blue-btn" onclick="startAnalysisPhase()">
                            <i class="fas fa-search"></i> Begin Investigation
                        </button>
                    </div>
                </div>
            `;

            updateStats(data);
        }

        // Start analysis phase
        async function startAnalysisPhase() {
            try {
                missionStartTime = Date.now();
                const response = await fetch('/player_vs_ai/start_analysis', { method: 'POST' });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                renderAnalysisPhase(data);
                updatePhaseIndicator('analysis');
                startInvestigationTimer(data.time_limit);

                // Play alert sound
                document.getElementById('alert-sound')?.play();

            } catch (error) {
                console.error('Error starting analysis:', error);
                showError('Failed to start analysis phase.');
            }
        }

        // Render analysis phase
        function renderAnalysisPhase(data) {
            const gameContent = document.getElementById('game-content');
            const alert = data.alert;

            gameContent.innerHTML = `
                <div class="alert-panel">
                    <h4><i class="fas fa-exclamation-triangle"></i> SECURITY ALERT</h4>
                    <div class="mb-2"><strong>Severity:</strong> ${alert.severity}/10</div>
                    <div class="mb-2"><strong>Title:</strong> ${alert.title}</div>
                    <div class="mb-3"><strong>Description:</strong> ${alert.description}</div>

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Affected Systems:</strong>
                            <ul class="mt-1">
                                ${alert.affected_systems.map(sys => `<li>${sys}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong>Indicators:</strong>
                            <ul class="mt-1">
                                ${alert.indicators.map(ind => `<li>${ind}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="investigation-panel">
                    <h5><i class="fas fa-search"></i> Investigation Tools</h5>
                    <div class="timer-display" id="investigation-timer">${data.time_limit}s</div>

                    <div class="command-grid" id="command-grid">
                        ${data.investigation_tools.available_commands.map(cmd => `
                            <div class="command-btn" onclick="runCommand('${cmd.name}')" data-command="${cmd.name}">
                                <div><strong>${cmd.name}</strong></div>
                                <div class="text-muted small">${cmd.description}</div>
                                <div class="text-warning small">Cost: ${cmd.cost}</div>
                            </div>
                        `).join('')}
                    </div>

                    <div id="command-output" class="mt-3" style="display: none;">
                        <h6>Command Output:</h6>
                        <pre class="code-block" id="output-text"></pre>
                        <div class="text-info small" id="relevance-score"></div>
                    </div>

                    <div class="text-center mt-3">
                        <button class="cyber-btn yellow-btn" onclick="getHint('investigation')" id="hint-btn">
                            <i class="fas fa-lightbulb"></i> Get Hint (${data.hints_available - hintsUsed} left)
                        </button>
                        <button class="cyber-btn green-btn" onclick="proceedToDecision()" id="proceed-btn" style="display: none;">
                            <i class="fas fa-arrow-right"></i> Proceed to Defense
                        </button>
                    </div>

                    <div class="hint-panel" id="hint-panel"></div>
                </div>
            `;

            usedCommands.clear();
        }

        // Run investigation command
        async function runCommand(command) {
            if (usedCommands.has(command)) return;

            try {
                const response = await fetch('/player_vs_ai/run_command', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ command: command })
                });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                // Mark command as used
                usedCommands.add(command);
                const commandBtn = document.querySelector(`[data-command="${command}"]`);
                commandBtn.classList.add('used');

                // Show output
                document.getElementById('command-output').style.display = 'block';
                document.getElementById('output-text').textContent = data.output;
                document.getElementById('relevance-score').textContent =
                    `Relevance Score: ${data.relevance_score}/10`;

                // Show proceed button after some investigation
                if (usedCommands.size >= 2) {
                    document.getElementById('proceed-btn').style.display = 'inline-block';
                }

            } catch (error) {
                console.error('Error running command:', error);
                showError('Failed to execute command.');
            }
        }

        // Proceed to decision phase
        async function proceedToDecision() {
            clearInterval(investigationTimer);

            try {
                const response = await fetch('/player_vs_ai/get_defense_options', { method: 'POST' });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                renderDecisionPhase(data);
                updatePhaseIndicator('decision');
                startDecisionTimer(data.time_limit);

            } catch (error) {
                console.error('Error getting defense options:', error);
                showError('Failed to load defense options.');
            }
        }

        // Render decision phase
        function renderDecisionPhase(data) {
            const gameContent = document.getElementById('game-content');

            gameContent.innerHTML = `
                <div class="investigation-panel">
                    <h5><i class="fas fa-shield-alt"></i> Choose Your Defense</h5>
                    <div class="timer-display" id="decision-timer">${data.time_limit}s</div>

                    <div class="mb-3">
                        <strong>Attack Summary:</strong> ${data.attack_summary.type}
                        (Severity: ${data.attack_summary.severity}/10, Urgency: ${data.attack_summary.urgency})
                    </div>

                    <div class="defense-options" id="defense-options">
                        ${data.options.map((option, index) => `
                            <div class="defense-option" onclick="selectDefenseOption(${index})" data-option="${index}">
                                <h6><strong>${option.name}</strong></h6>
                                <p class="small">${option.description}</p>

                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: ${option.confidence_level.percentage}%"></div>
                                </div>
                                <div class="small text-center mt-1">Intelligence Confidence: ${option.confidence_level.percentage}%</div>

                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small><strong>Risk:</strong> ${option.risk_assessment.level}</small>
                                    </div>
                                    <div class="col-6">
                                        <small><strong>Complexity:</strong> ${option.complexity.level}</small>
                                    </div>
                                </div>

                                <div class="learning-tip">
                                    <i class="fas fa-lightbulb"></i> ${option.learning_tip}
                                </div>

                                <div class="mt-2">
                                    <span class="badge bg-${option.risk_assessment.level.toLowerCase() === 'low' ? 'success' :
                                        option.risk_assessment.level.toLowerCase() === 'medium' ? 'warning' : 'danger'}">
                                        ${option.risk_assessment.level} Risk
                                    </span>
                                    <div class="small text-muted mt-1">${option.risk_assessment.description}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="text-center mt-3">
                        <button class="cyber-btn yellow-btn" onclick="getHint('defense')" id="defense-hint-btn">
                            <i class="fas fa-lightbulb"></i> Get Defense Hint
                        </button>
                        <button class="cyber-btn green-btn" onclick="executeDecision()" id="execute-btn" style="display: none;">
                            <i class="fas fa-play"></i> Execute Defense
                        </button>
                    </div>

                    <div class="hint-panel" id="defense-hint-panel"></div>
                </div>
            `;
        }

        // Select defense option
        function selectDefenseOption(optionIndex) {
            // Remove previous selection
            document.querySelectorAll('.defense-option').forEach(opt => opt.classList.remove('selected'));

            // Select new option
            const option = document.querySelector(`[data-option="${optionIndex}"]`);
            option.classList.add('selected');
            selectedDefenseOption = optionIndex;

            // Show execute button
            document.getElementById('execute-btn').style.display = 'inline-block';
        }

        // Execute player decision
        async function executeDecision() {
            if (selectedDefenseOption === null) {
                showError('Please select a defense option first.');
                return;
            }

            clearInterval(decisionTimer);
            const responseTime = (Date.now() - missionStartTime) / 1000;

            try {
                const response = await fetch('/player_vs_ai/execute_decision', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        chosen_option_id: selectedDefenseOption,
                        response_time: responseTime
                    })
                });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                renderResolutionPhase(data);
                updatePhaseIndicator('resolution');
                updateStats(data);

                // Play appropriate sound
                if (data.result.round_winner === 'Player Blue Team') {
                    document.getElementById('success-sound')?.play();
                } else {
                    document.getElementById('alert-sound')?.play();
                }

            } catch (error) {
                console.error('Error executing decision:', error);
                showError('Failed to execute defense.');
            }
        }

        // Render resolution phase
        function renderResolutionPhase(data) {
            const gameContent = document.getElementById('game-content');
            const result = data.result;
            const performance = data.performance_feedback;

            gameContent.innerHTML = `
                <div class="mission-briefing">
                    <div class="mission-title">
                        <i class="fas fa-flag-checkered"></i> Mission ${data.round} Complete
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Round Results</h5>
                            <div class="mb-2">
                                <strong>Winner:</strong>
                                <span class="${result.round_winner === 'Player Blue Team' ? 'text-success' : 'text-danger'}">
                                    ${result.round_winner}
                                </span>
                            </div>
                            <div class="mb-2"><strong>Defense Impact:</strong> ${result.defense_effectiveness.toFixed(1)}/10</div>
                            <div class="mb-2"><strong>Time Bonus:</strong> +${result.time_bonus.toFixed(1)}</div>
                            <div class="mb-2"><strong>Streak Bonus:</strong> +${result.streak_bonus}</div>
                        </div>
                        <div class="col-md-6">
                            <h5>Performance</h5>
                            <div class="mb-2"><strong>Grade:</strong> ${performance.grade}</div>
                            <div class="mb-2"><strong>Accuracy:</strong> ${performance.accuracy}%</div>
                            <div class="mb-2"><strong>Response Time:</strong> ${performance.avg_response_time.toFixed(1)}s</div>
                            <div class="mb-2"><strong>Confidence:</strong> ${performance.confidence_level}</div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5><i class="fas fa-lightbulb"></i> Learning Insights</h5>
                        <ul>
                            ${data.learning_insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="text-center">
                        ${currentMission < maxMissions ? `
                            <button class="cyber-btn blue-btn" onclick="startNextMission()">
                                <i class="fas fa-arrow-right"></i> Next Mission
                            </button>
                        ` : `
                            <button class="cyber-btn green-btn" onclick="showFinalResults()">
                                <i class="fas fa-trophy"></i> View Final Results
                            </button>
                        `}
                        <button class="cyber-btn" onclick="restartTraining()">
                            <i class="fas fa-redo"></i> Restart Training
                        </button>
                    </div>
                </div>
            `;

            selectedDefenseOption = null;
            hintsUsed = 0;
        }

        // Start next mission
        async function startNextMission() {
            try {
                const response = await fetch('/player_vs_ai/start_briefing', { method: 'POST' });
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                currentMission = data.round;
                renderMissionBriefing(data);
                updateProgressBar(currentMission, maxMissions);
                updatePhaseIndicator('briefing');

            } catch (error) {
                console.error('Error starting next mission:', error);
                showError('Failed to start next mission.');
            }
        }

        // Timer functions
        function startInvestigationTimer(seconds) {
            let timeLeft = seconds;
            const timerDisplay = document.getElementById('investigation-timer');

            investigationTimer = setInterval(() => {
                timeLeft--;
                timerDisplay.textContent = timeLeft + 's';

                if (timeLeft <= 10) {
                    timerDisplay.classList.add('urgent');
                    if (timeLeft <= 5) {
                        document.getElementById('timer-sound')?.play();
                    }
                }

                if (timeLeft <= 0) {
                    clearInterval(investigationTimer);
                    proceedToDecision();
                }
            }, 1000);
        }

        function startDecisionTimer(seconds) {
            let timeLeft = seconds;
            const timerDisplay = document.getElementById('decision-timer');

            decisionTimer = setInterval(() => {
                timeLeft--;
                timerDisplay.textContent = timeLeft + 's';

                if (timeLeft <= 10) {
                    timerDisplay.classList.add('urgent');
                    if (timeLeft <= 5) {
                        document.getElementById('timer-sound')?.play();
                    }
                }

                if (timeLeft <= 0) {
                    clearInterval(decisionTimer);
                    // Auto-select first option if none selected
                    if (selectedDefenseOption === null) {
                        selectDefenseOption(0);
                    }
                    executeDecision();
                }
            }, 1000);
        }

        // Get hint
        async function getHint(hintType) {
            try {
                const response = await fetch('/player_vs_ai/get_hint', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ hint_type: hintType })
                });
                const data = await response.json();

                const hintPanel = document.getElementById(hintType === 'defense' ? 'defense-hint-panel' : 'hint-panel');
                hintPanel.innerHTML = `<i class="fas fa-lightbulb"></i> <strong>Hint:</strong> ${data}`;
                hintPanel.classList.add('show');

                hintsUsed++;

                // Update hint button
                const hintBtn = document.getElementById(hintType === 'defense' ? 'defense-hint-btn' : 'hint-btn');
                if (hintsUsed >= 3) {
                    hintBtn.style.display = 'none';
                }

            } catch (error) {
                console.error('Error getting hint:', error);
                showError('Failed to get hint.');
            }
        }

        // Update UI functions
        function updateProgressBar(current, max) {
            const progressBar = document.getElementById('mission-progress-bar');
            const progressText = document.getElementById('mission-progress-text');

            const percentage = (current / max) * 100;
            progressBar.style.width = percentage + '%';
            progressText.textContent = `Mission ${current} / ${max}`;
        }

        function updatePhaseIndicator(phase) {
            // Reset all phases
            document.querySelectorAll('.phase-step').forEach(step => {
                step.classList.remove('active', 'completed');
            });

            // Mark completed phases
            const phases = ['briefing', 'analysis', 'decision', 'resolution'];
            const currentIndex = phases.indexOf(phase);

            for (let i = 0; i < currentIndex; i++) {
                document.getElementById(`phase-${phases[i]}`).classList.add('completed');
            }

            // Mark current phase
            document.getElementById(`phase-${phase}`).classList.add('active');
            gamePhase = phase;
        }

        function updateStats(data) {
            if (data.blue_score !== undefined) {
                document.getElementById('blue-score').textContent = data.blue_score;
            }
            if (data.red_score !== undefined) {
                document.getElementById('red-score').textContent = data.red_score;
            }
            if (data.system_health !== undefined) {
                const healthFill = document.getElementById('health-fill');
                const healthText = document.getElementById('health-text');
                healthFill.style.width = data.system_health + '%';
                healthText.textContent = data.system_health + '%';

                // Update health bar color
                if (data.system_health > 70) {
                    healthFill.style.background = 'var(--green-success)';
                } else if (data.system_health > 30) {
                    healthFill.style.background = 'var(--yellow-warning)';
                } else {
                    healthFill.style.background = 'var(--red-primary)';
                }
            }
            if (data.player_confidence !== undefined) {
                document.getElementById('confidence-stat').textContent = data.player_confidence;
            }

            // Update performance stats if available
            if (data.performance_feedback) {
                const perf = data.performance_feedback;
                document.getElementById('accuracy-stat').textContent = perf.accuracy + '%';
                document.getElementById('learning-progress').textContent = perf.learning_progress + '%';

                // Update progress ring
                const circle = document.getElementById('progress-circle');
                const circumference = 2 * Math.PI * 25;
                const offset = circumference - (perf.learning_progress / 100) * circumference;
                circle.style.strokeDashoffset = offset;
            }

            if (data.result && data.result.streak_bonus !== undefined) {
                document.getElementById('streak-stat').textContent = data.result.streak_bonus;
            }
        }

        // Utility functions
        function showError(message) {
            const gameContent = document.getElementById('game-content');
            gameContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
                <div class="text-center mt-3">
                    <button class="cyber-btn" onclick="location.reload()">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }

        async function restartTraining() {
            try {
                await fetch('/player_vs_ai/reset', { method: 'POST' });
                location.reload();
            } catch (error) {
                console.error('Error restarting training:', error);
                location.reload();
            }
        }

        async function showFinalResults() {
            try {
                const response = await fetch('/player_vs_ai/get_statistics', { method: 'GET' });
                const stats = await response.json();

                const gameContent = document.getElementById('game-content');
                gameContent.innerHTML = `
                    <div class="mission-briefing">
                        <div class="mission-title">
                            <i class="fas fa-trophy"></i> Training Complete!
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>Final Statistics</h5>
                                <div class="mb-2"><strong>Overall Accuracy:</strong> ${stats.accuracy.toFixed(1)}%</div>
                                <div class="mb-2"><strong>Average Response Time:</strong> ${stats.avg_response_time.toFixed(1)}s</div>
                                <div class="mb-2"><strong>Learning Progress:</strong> ${stats.learning_progress}%</div>
                                <div class="mb-2"><strong>Missions Completed:</strong> ${stats.missions_completed}/${stats.total_missions}</div>
                            </div>
                            <div class="col-md-6">
                                <h5>Performance Grade</h5>
                                <div class="text-center">
                                    <div class="stat-value" style="font-size: 3rem;">
                                        ${stats.accuracy >= 80 ? 'A' : stats.accuracy >= 60 ? 'B' : 'C'}
                                    </div>
                                    <div class="text-muted">
                                        ${stats.accuracy >= 80 ? 'Excellent!' : stats.accuracy >= 60 ? 'Good Job!' : 'Keep Practicing!'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="cyber-btn blue-btn" onclick="restartTraining()">
                                <i class="fas fa-redo"></i> Train Again
                            </button>
                            <a href="/" class="cyber-btn">
                                <i class="fas fa-home"></i> Main Menu
                            </a>
                        </div>
                    </div>
                `;

            } catch (error) {
                console.error('Error getting final results:', error);
                showError('Failed to load final results.');
            }
        }
    </script>
</body>
</html>
