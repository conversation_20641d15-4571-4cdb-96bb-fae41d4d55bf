<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Cyberwar Operations - AI-powered cybersecurity simulation featuring Red Team vs Blue Team warfare">
    <meta name="keywords" content="cybersecurity, red team, blue team, simulation, AI, infrastructure">
    <title>CYBERWAR OPS - Red Team vs Blue Team</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="cyber-logo">
                <i class="fas fa-shield-alt"></i>
                <span>CYBERWAR OPS</span>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">INITIALIZING CYBER WARFARE SIMULATION...</div>
            <button onclick="skipLoading()" style="margin-top: 20px; padding: 10px 20px; background: var(--blue-primary); color: white; border: none; border-radius: 5px; cursor: pointer;">SKIP LOADING</button>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="main-app d-none">
        <!-- Header -->
        <header class="cyber-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="logo-section">
                            <i class="fas fa-shield-alt"></i>
                            <span class="logo-text">CYBERWAR OPS</span>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="mission-status">
                            <span id="mission-timer" class="timer">05:00</span>
                            <div class="status-text">MISSION ACTIVE</div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="threat-level">
                            <span class="threat-indicator" id="threat-level">DEFCON 3</span>
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Game Mode Selection -->
        <div id="game-mode-selection" class="game-mode-selection">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="mode-selector">
                            <h1 class="mode-title">SELECT OPERATION MODE</h1>
                            <div class="mode-grid">
                                <div class="mode-card" onclick="startPlayerVsAIGame()" role="button" tabindex="0" aria-label="Start Player vs AI Mode - Interactive Training">
                                    <div class="mode-icon" aria-hidden="true">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h3>PLAYER vs AI</h3>
                                    <p>Interactive cybersecurity training missions</p>
                                    <div class="mode-badge">TRAINING</div>
                                </div>
                                <div class="mode-card" onclick="startGame('pvp')" role="button" tabindex="0" aria-label="Start Warfare Mode - Player vs Player">
                                    <div class="mode-icon" aria-hidden="true">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h3>WARFARE MODE</h3>
                                    <p>Human vs Human cyber warfare</p>
                                    <div class="mode-badge">PvP</div>
                                </div>
                                <div class="mode-card" onclick="startEduGame()" role="button" tabindex="0" aria-label="Start AI vs AI Educational Mode">
                                    <div class="mode-icon" style="color: var(--green-success);" aria-hidden="true">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <h3>AI vs AI MODE</h3>
                                    <p>A guided walkthrough of cyber attacks</p>
                                    <div class="mode-badge" style="background: var(--green-success);">EDUCATIONAL</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Dashboard -->
        <div id="game-dashboard" class="game-dashboard d-none">
            <div class="container-fluid">
                <!-- Team Status Row -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="team-panel red-team">
                            <div class="team-header">
                                <i class="fas fa-skull"></i>
                                <span>RED TEAM</span>
                                <div class="team-status">ATTACKING</div>
                            </div>
                            <div class="team-stats">
                                <div class="stat">
                                    <span class="stat-label">ATTACKS</span>
                                    <span class="stat-value" id="red-attacks">0</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">SUCCESS RATE</span>
                                    <span class="stat-value" id="red-success">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="team-panel blue-team">
                            <div class="team-header">
                                <i class="fas fa-shield-alt"></i>
                                <span>BLUE TEAM</span>
                                <div class="team-status">DEFENDING</div>
                            </div>
                            <div class="team-stats">
                                <div class="stat">
                                    <span class="stat-label">BLOCKS</span>
                                    <span class="stat-value" id="blue-blocks">0</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">DEFENSE RATE</span>
                                    <span class="stat-value" id="blue-defense">100%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Row -->
                <div class="row">
                    <!-- Left Panel - System Health & Controls -->
                    <div class="col-lg-3">
                        <div class="cyber-panel">
                            <div class="panel-header">
                                <i class="fas fa-heartbeat"></i>
                                <span>SYSTEM INTEGRITY</span>
                            </div>
                            <div class="panel-content">
                                <div class="health-display">
                                    <div class="health-circle">
                                        <svg class="health-svg" viewBox="0 0 100 100">
                                            <circle class="health-bg" cx="50" cy="50" r="45"></circle>
                                            <circle class="health-progress" cx="50" cy="50" r="45" id="health-circle"></circle>
                                        </svg>
                                        <div class="health-text">
                                            <span id="health-percentage">100</span>%
                                        </div>
                                    </div>
                                    <div class="health-status" id="health-status">SECURE</div>
                                </div>

                                <!-- Blue Team Controls (PvE Mode) -->
                                <div id="blue-controls" class="controls-section d-none">
                                    <div class="controls-header">
                                        <i class="fas fa-terminal"></i>
                                        <span>BLUE TEAM COMMANDS</span>
                                    </div>
                                    <div class="command-buttons">
                                        <button class="cyber-btn blue-btn" onclick="sendCoachingCommand('blue', 'patch_critical')">
                                            <i class="fas fa-tools"></i>
                                            PATCH CRITICAL
                                        </button>
                                        <button class="cyber-btn blue-btn" onclick="sendCoachingCommand('blue', 'monitor_traffic')">
                                            <i class="fas fa-eye"></i>
                                            MONITOR TRAFFIC
                                        </button>
                                        <button class="cyber-btn blue-btn" onclick="sendCoachingCommand('blue', 'firewall_lockdown')">
                                            <i class="fas fa-lock"></i>
                                            FIREWALL LOCKDOWN
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Center Panel - Infrastructure Visualization -->
                    <div class="col-lg-6">
                        <div class="cyber-panel">
                            <div class="panel-header">
                                <i class="fas fa-network-wired"></i>
                                <span>INFRASTRUCTURE MAP</span>
                            </div>
                            <div class="panel-content">
                                <div id="infra-visualization" class="infra-viz">
                                    <!-- Infrastructure nodes will be dynamically generated -->
                                </div>

                                <!-- Code Viewer -->
                                <div id="code-viewer" class="code-viewer">
                                    <div class="code-tabs">
                                        <div class="code-tab active" data-tab="vulnerable">
                                            <i class="fas fa-bug"></i>
                                            VULNERABLE CODE
                                        </div>
                                        <div class="code-tab" data-tab="secured">
                                            <i class="fas fa-shield-check"></i>
                                            SECURED CODE
                                        </div>
                                    </div>
                                    <div class="code-content">
                                        <div class="code-panel active" id="vulnerable-code">
                                            <pre><code id="vulnerable-code-content">// Waiting for infrastructure...</code></pre>
                                        </div>
                                        <div class="code-panel" id="secured-code">
                                            <pre><code id="secured-code-content">// No fixes applied yet...</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel - Intelligence Feed -->
                    <div class="col-lg-3">
                        <div class="cyber-panel">
                            <div class="panel-header">
                                <i class="fas fa-satellite-dish"></i>
                                <span>INTELLIGENCE FEED</span>
                            </div>
                            <div class="panel-content">
                                <div id="intelligence-feed" class="intelligence-feed">
                                    <!-- Intelligence messages will be dynamically added -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attack Animation Overlay -->
    <div id="attack-overlay" class="attack-overlay d-none">
        <div class="attack-animation">
            <div class="attack-wave"></div>
            <div class="attack-text">CYBER ATTACK DETECTED</div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/socket.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/game.js') }}"></script>
    <script src="{{ url_for('static', filename='js/animations.js') }}"></script>
    <script>
        async function startEduGame() {
            // Reset any existing educational game session on the server
            try {
                await fetch('/ai_vs_ai_edu_reset');
            } catch (error) {
                console.error("Failed to reset educational game session:", error);
            }
            // Navigate to the educational mode view
            window.location.href = '/ai_vs_ai_edu_view';
        }

        async function startPlayerVsAIGame() {
            // Reset any existing player vs AI game session on the server
            try {
                await fetch('/player_vs_ai/reset', { method: 'POST' });
            } catch (error) {
                console.error("Failed to reset player vs AI game session:", error);
            }
            // Navigate to the player vs AI mode view
            window.location.href = '/player_vs_ai_mode';
        }
    </script>
</body>
</html>
